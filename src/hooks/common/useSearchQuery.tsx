import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import { parse, stringify } from 'qs-esm'
// import { useRouter<PERSON>oader } from './useRouterLoader'
export const useSearchQuery = () => {
  const searchParams = useSearchParams()
  const pathname = usePathname()
  const router = useRouter()

  const updateSearchQuery = (
    updatedQuery: { [key: string]: any },
    behavior: 'push' | 'replace' = 'replace',
    scroll?: boolean,
    options?: { reset?: boolean },
  ) => {
    let finalParams

    if (options?.reset) {
      finalParams = { ...updatedQuery }
    } else {
      const currentParams = parse(searchParams.toString(), {
        ignoreQueryPrefix: true,
      })
      finalParams = { ...currentParams, ...updatedQuery }
    }

    const queryString = stringify(finalParams, { skipNulls: true })

    const updatedPath = queryString ? `${pathname}?${queryString}` : pathname

    if (behavior === 'push') router.push(updatedPath, { scroll })
    if (behavior === 'replace') router.replace(updatedPath, { scroll })
  }
  // const getAllSearchQueries = () => {
  //     const result: { [key: string]: string | string[] } = {};
  //     const entries = Array.from(searchParams.entries());

  //     for (const [key, value] of entries) {
  //         if (result[key]) {
  //             // If the key already exists, convert the value to an array
  //             if (Array.isArray(result[key])) {
  //                 (result[key] as string[]).push(value);
  //             } else {
  //                 result[key] = [result[key] as string, value];
  //             }
  //         } else {
  //             result[key] = value;
  //         }
  //     }

  //     return result;
  // };
  const getAllSearchQueries = () => {
    return Object.fromEntries(searchParams.entries())
  }
  const removeAllSearchQueries = () => {
    router.replace(pathname)
  }
  return { updateSearchQuery, getAllSearchQueries, removeAllSearchQueries }
}
