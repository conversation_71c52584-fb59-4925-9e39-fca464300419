import { useTranslations } from 'next-intl'
import ProductItem from './ProductItem'
import { Product } from '@/payload-types'
import ProductSkeleton from './ProductSkeleton'

type ProductListType = {
  productListData: Product[]
  isLoading: boolean
}
const ProductList: React.FC<ProductListType> = ({ productListData, isLoading }) => {
  const t = useTranslations()

  return isLoading ? (
    <ProductSkeleton />
  ) : (
    <>
      <div className="typo-body-3 mt-3 flex items-center gap-3">
        {t('MES-660')} ({productListData.length})
      </div>

      <div className="mt-3">
        <div className="grid grid-cols-6 gap-3">
          {productListData.map((product) => {
            return (
              <div key={product.id}>
                <ProductItem productData={product} />
              </div>
            )
          })}
        </div>
      </div>
    </>
  )
}

export default ProductList
