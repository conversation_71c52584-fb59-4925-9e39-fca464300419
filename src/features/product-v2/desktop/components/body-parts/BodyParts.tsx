'use client'

import Image from 'next/image'

import ArrowRightIcon from '@/assets/icons/arrow-right-gray.svg'
import ArrowDownIcon from '@/assets/icons/arrow-right-primary.svg'
import { useGetCategoriesV2 } from '@/features/product-v2/hooks/query/useGetCategoriesV2'
import { useLocale, useTranslations } from 'next-intl'
import { ProductV2TypeEnum } from '@/features/product-v2/enums'
import React, { useEffect, useState } from 'react'
import { useSearchQuery } from '@/hooks/common/useSearchQuery'
import { useGetProductNestCategories } from '@/features/product-v2/hooks/query/useGetProductNestCategories'
import BodyPartSkeleton from './BodyPartSekeleton'
import SectionView from './SectionView'
import { Button } from '@/components/ui/Button/Button'

const BodyParts: React.FC = () => {
  const locale = useLocale()

  const t = useTranslations()
  const { getAllSearchQueries, updateSearchQuery } = useSearchQuery()

  const [typeFilter, setTypeFilter] = useState<ProductV2TypeEnum[]>([ProductV2TypeEnum.MEDICINE])

  const [categorySelected, setCategorySelected] = useState<
    Record<string, { name: string; id: string }>
  >({})

  const { category, categoryId } = getAllSearchQueries()

  const limit = 20

  useEffect(() => {
    if (category && Object.values(ProductV2TypeEnum).includes(category as ProductV2TypeEnum)) {
      setTypeFilter([category as ProductV2TypeEnum])
    } else {
      // Default to MEDICINE if no valid category in URL
      setTypeFilter([ProductV2TypeEnum.MEDICINE])
    }
  }, [category])

  const { categoriesV2, isLoading } = useGetCategoriesV2({
    config: {
      staleTime: 5 * 60 * 1000,
    },
    params: {
      limit,
      depth: 1,
      locale: locale ?? 'vi',
      where: {
        and: [
          {
            type: {
              equals: typeFilter[0],
            },
          },
          {
            parent: {
              exists: false,
            },
          },
          {
            categoryLevel: {
              equals: 0,
            },
          },
        ],
      },
      select: {
        id: true,
        title: true,
        icon: true,
      },
    },
  })

  const bodyParts = categoriesV2?.pages[0].docs ?? []
  const totalCategoriesV2 = categoriesV2?.pages[0].totalDocs ?? 0
  const currentPageCategoriesV2 = categoriesV2?.pages[0].page ?? 1
  const isHasNextBodyPartPage = categoriesV2?.pages[0]?.hasNextPage ?? false

  const handleSelectCategory = (idCategories: string, name: string) => {
    updateSearchQuery({ categoryId: idCategories }, 'replace')

    if (categorySelected[idCategories]) {
      return
    }
    setCategorySelected({ [idCategories]: { name, id: idCategories } })
  }

  useEffect(() => {
    const category = bodyParts.find((bodyPart) => bodyPart.id === categoryId)
    if (category) {
      handleSelectCategory(categoryId, category.title)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [categoryId, bodyParts])

  const { productNestCategories, isLoading: isProductNestCategoriesLoading } =
    useGetProductNestCategories({
      categoryId: categoryId || '',
      config: {
        staleTime: 5 * 60 * 1000,
        enabled: !!categoryId,
      },
      params: {
        locale: locale ?? 'vi',
        limit,
        where: {
          and: [
            {
              type: {
                in: typeFilter,
              },
            },
          ],
        },
      },
    })

  const productNestCategoriesData = productNestCategories?.pages[0].docs ?? []
  const totalProductNestCategories = productNestCategories?.pages[0].totalDocs ?? 0
  const currentPageProductNestCategories = productNestCategories?.pages[0].page ?? 1
  const isHasNextProductPage = productNestCategories?.pages[0]?.hasNextPage ?? false

  return isLoading || isProductNestCategoriesLoading ? (
    <BodyPartSkeleton />
  ) : (
    <>
      <div className="mt-3 flex items-center gap-2">
        {Object.values(categorySelected).map((category, index) => (
          <React.Fragment key={category.id}>
            <div className="typo-body-3">{category.name}</div>
            {index !== 0 && (
              <Image
                src={ArrowRightIcon}
                alt="arrow-icon"
                width={14}
                height={14}
                className="size-4"
              />
            )}
          </React.Fragment>
        ))}
      </div>
      <div className="mt-3 grid grid-cols-6 gap-3">
        {categoryId ? (
          <SectionView
            isViewImage={false}
            sectionData={productNestCategoriesData}
            handleSelectCategory={handleSelectCategory}
          />
        ) : (
          <SectionView sectionData={bodyParts} handleSelectCategory={handleSelectCategory} />
        )}
      </div>

      <div className="flex items-center justify-center">
        {categoryId
          ? isHasNextProductPage && (
              <Button variant={'blank'} className="flex items-center gap-2 p-0 text-primary-500">
                {t('MES-741', {
                  number: totalProductNestCategories - limit * currentPageProductNestCategories,
                })}
                <Image src={ArrowDownIcon} alt={'arrow down'} className="size-4 rotate-90" />
              </Button>
            )
          : isHasNextBodyPartPage && (
              <Button variant={'blank'} className="flex items-center gap-2 p-0 text-primary-500">
                {t('MES-741', { number: totalCategoriesV2 - limit * currentPageCategoriesV2 })}
                <Image src={ArrowDownIcon} alt={'arrow down'} className="size-4 rotate-90" />
              </Button>
            )}
      </div>
    </>
  )
}

export default BodyParts
