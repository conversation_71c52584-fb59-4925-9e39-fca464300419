'use client'

import Image from 'next/image'

import ArrowRightIcon from '@/assets/icons/arrow-right-gray.svg'
import ArrowDownIcon from '@/assets/icons/arrow-right-primary.svg'
import { useGetCategoriesV2 } from '@/features/product-v2/hooks/query/useGetCategoriesV2'
import { useLocale, useTranslations } from 'next-intl'
import { ProductV2TypeEnum } from '@/features/product-v2/enums'
import React, { useEffect, useState, useMemo } from 'react'
import { useSearchQuery } from '@/hooks/common/useSearchQuery'
import { useGetProductNestCategories } from '@/features/product-v2/hooks/query/useGetProductNestCategories'
import BodyPartSkeleton from './BodyPartSekeleton'
import SectionView from './SectionView'
import { Button } from '@/components/ui/Button/Button'

interface CategoryBreadcrumb {
  id: string
  name: string
}

const BodyParts: React.FC = () => {
  const locale = useLocale()

  const t = useTranslations()
  const { getAllSearchQueries, updateSearchQuery } = useSearchQuery()

  const [typeFilter, setTypeFilter] = useState<ProductV2TypeEnum[]>([ProductV2TypeEnum.MEDICINE])

  // Breadcrumb để lưu trữ các categories đã chọn
  const [categoryBreadcrumb, setCategoryBreadcrumb] = useState<CategoryBreadcrumb[]>([])

  // State để track số lượng items hiển thị
  const [visibleCategoriesCount, setVisibleCategoriesCount] = useState(20)
  const [visibleNestedCategoriesCount, setVisibleNestedCategoriesCount] = useState(20)

  const { category, categoryIds } = getAllSearchQueries()

  const limit = 20

  const currentCategoryId = useMemo(() => {
    return categoryBreadcrumb.length > 0
      ? categoryBreadcrumb[categoryBreadcrumb.length - 1].id
      : null
  }, [categoryBreadcrumb])

  useEffect(() => {
    if (category && Object.values(ProductV2TypeEnum).includes(category as ProductV2TypeEnum)) {
      setTypeFilter([category as ProductV2TypeEnum])
    } else {
      // Default to MEDICINE if no valid category in URL
      setTypeFilter([ProductV2TypeEnum.MEDICINE])
    }
  }, [category])

  useEffect(() => {
    if (categoryIds && typeof categoryIds === 'string') {
      try {
        const parsedIds = JSON.parse(decodeURIComponent(categoryIds)) as CategoryBreadcrumb[]
        if (Array.isArray(parsedIds)) {
          setCategoryBreadcrumb(parsedIds)
        }
      } catch (error) {
        console.error('Error parsing categoryIds from URL:', error)
      }
    }
  }, [categoryIds])

  // Reset visible counts khi chuyển category
  useEffect(() => {
    setVisibleCategoriesCount(limit)
    setVisibleNestedCategoriesCount(limit)
  }, [currentCategoryId])

  const {
    categoriesV2,
    isLoading,
    fetchNextPage: fetchNextCategoriesPage,
    hasNextPage: hasNextCategoriesPage,
  } = useGetCategoriesV2({
    config: {
      staleTime: 5 * 60 * 1000,
    },
    params: {
      limit,
      depth: 1,
      locale: locale ?? 'vi',
      where: {
        and: [
          {
            type: {
              equals: typeFilter[0],
            },
          },
          {
            parent: {
              exists: false,
            },
          },
          {
            categoryLevel: {
              equals: 0,
            },
          },
        ],
      },
      select: {
        id: true,
        title: true,
        icon: true,
      },
    },
  })

  // Flatten tất cả pages của categories
  const allBodyParts = useMemo(() => {
    return categoriesV2?.pages.flatMap((page) => page.docs) ?? []
  }, [categoriesV2])

  // Chỉ hiển thị số lượng items theo visibleCategoriesCount
  const bodyParts = useMemo(() => {
    return allBodyParts.slice(0, visibleCategoriesCount)
  }, [allBodyParts, visibleCategoriesCount])

  const totalCategoriesV2 = categoriesV2?.pages[0]?.totalDocs ?? 0
  const loadedCategoriesCount = allBodyParts.length
  const hasMoreCategories = hasNextCategoriesPage || visibleCategoriesCount < loadedCategoriesCount

  const handleSelectCategory = (idCategories: string, name: string) => {
    const newBreadcrumb = [...categoryBreadcrumb, { id: idCategories, name }]
    setCategoryBreadcrumb(newBreadcrumb)

    // Lưu breadcrumb lên URL
    updateSearchQuery(
      {
        categoryIds: encodeURIComponent(JSON.stringify(newBreadcrumb)),
      },
      'replace',
    )
  }

  const handleBreadcrumbClick = (index: number) => {
    const newBreadcrumb = categoryBreadcrumb.slice(0, index + 1)
    setCategoryBreadcrumb(newBreadcrumb)

    // Cập nhật URL
    if (newBreadcrumb.length === 0) {
      updateSearchQuery({ categoryIds: undefined }, 'replace')
    } else {
      updateSearchQuery(
        {
          categoryIds: encodeURIComponent(JSON.stringify(newBreadcrumb)),
        },
        'replace',
      )
    }
  }

  const {
    productNestCategories,
    isLoading: isProductNestCategoriesLoading,
    fetchNextPage: fetchNextNestedPage,
    hasNextPage: hasNextNestedPage,
  } = useGetProductNestCategories({
    categoryId: currentCategoryId || '',
    config: {
      staleTime: 5 * 60 * 1000,
      enabled: !!currentCategoryId,
    },
    params: {
      locale: locale ?? 'vi',
      limit,
      where: {
        and: [
          {
            type: {
              in: typeFilter,
            },
          },
        ],
      },
    },
  })

  // Flatten tất cả pages của nested categories
  const allProductNestCategoriesData = useMemo(() => {
    return productNestCategories?.pages.flatMap((page) => page.docs) ?? []
  }, [productNestCategories])

  // Chỉ hiển thị số lượng items theo visibleNestedCategoriesCount
  const productNestCategoriesData = useMemo(() => {
    return allProductNestCategoriesData.slice(0, visibleNestedCategoriesCount)
  }, [allProductNestCategoriesData, visibleNestedCategoriesCount])

  const totalProductNestCategories = productNestCategories?.pages[0]?.totalDocs ?? 0
  const loadedNestedCategoriesCount = allProductNestCategoriesData.length
  const hasMoreNestedCategories =
    hasNextNestedPage || visibleNestedCategoriesCount < loadedNestedCategoriesCount

  // Load more functions
  const handleLoadMoreCategories = () => {
    // Nếu còn items đã load nhưng chưa hiển thị, hiển thị thêm
    if (visibleCategoriesCount < loadedCategoriesCount) {
      setVisibleCategoriesCount((prev) => Math.min(prev + limit, loadedCategoriesCount))
    }
    // Nếu đã hiển thị hết items đã load, fetch thêm từ server
    else if (hasNextCategoriesPage) {
      fetchNextCategoriesPage()
    }
  }

  const handleLoadMoreNestedCategories = () => {
    // Nếu còn items đã load nhưng chưa hiển thị, hiển thị thêm
    if (visibleNestedCategoriesCount < loadedNestedCategoriesCount) {
      setVisibleNestedCategoriesCount((prev) => Math.min(prev + limit, loadedNestedCategoriesCount))
    }
    // Nếu đã hiển thị hết items đã load, fetch thêm từ server
    else if (hasNextNestedPage) {
      fetchNextNestedPage()
    }
  }

  // Show less functions
  const handleShowLessCategories = () => {
    setVisibleCategoriesCount(limit)
  }

  const handleShowLessNestedCategories = () => {
    setVisibleNestedCategoriesCount(limit)
  }

  return isLoading || isProductNestCategoriesLoading ? (
    <BodyPartSkeleton />
  ) : (
    <>
      {/* Breadcrumb */}
      <div className="mt-3 flex items-center gap-2">
        {categoryBreadcrumb.length > 0 && (
          <>
            <div
              className="typo-body-3 cursor-pointer hover:text-primary-500"
              onClick={() => {
                setCategoryBreadcrumb([])
                updateSearchQuery({ categoryIds: undefined }, 'replace')
              }}
            >
              Home
            </div>
            <Image
              src={ArrowRightIcon}
              alt="arrow-icon"
              width={14}
              height={14}
              className="size-4"
            />
          </>
        )}
        {categoryBreadcrumb.map((category, index) => (
          <React.Fragment key={category.id}>
            <div
              className="typo-body-3 cursor-pointer hover:text-primary-500"
              onClick={() => handleBreadcrumbClick(index)}
            >
              {category.name}
            </div>
            {index < categoryBreadcrumb.length - 1 && (
              <Image
                src={ArrowRightIcon}
                alt="arrow-icon"
                width={14}
                height={14}
                className="size-4"
              />
            )}
          </React.Fragment>
        ))}
      </div>

      {/* Categories Grid */}
      <div className="mt-3 grid grid-cols-6 gap-3">
        {currentCategoryId ? (
          <SectionView
            isViewImage={false}
            sectionData={productNestCategoriesData}
            handleSelectCategory={handleSelectCategory}
          />
        ) : (
          <SectionView sectionData={bodyParts} handleSelectCategory={handleSelectCategory} />
        )}
      </div>

      {/* Load More/Show Less Buttons */}
      <div className="flex items-center justify-center gap-4">
        {currentCategoryId ? (
          <>
            {hasMoreNestedCategories && (
              <Button
                variant={'blank'}
                className="flex items-center gap-2 p-0 text-primary-500"
                onClick={handleLoadMoreNestedCategories}
              >
                {t('MES-741', {
                  number: Math.max(0, totalProductNestCategories - visibleNestedCategoriesCount),
                })}
                <Image src={ArrowDownIcon} alt={'arrow down'} className="size-4 rotate-90" />
              </Button>
            )}
            {visibleNestedCategoriesCount > limit && (
              <Button
                variant={'blank'}
                className="flex items-center gap-2 p-0 text-gray-500"
                onClick={handleShowLessNestedCategories}
              >
                Show Less
                <Image src={ArrowDownIcon} alt={'arrow up'} className="size-4 -rotate-90" />
              </Button>
            )}
          </>
        ) : (
          <>
            {hasMoreCategories && (
              <Button
                variant={'blank'}
                className="flex items-center gap-2 p-0 text-primary-500"
                onClick={handleLoadMoreCategories}
              >
                {t('MES-741', { number: Math.max(0, totalCategoriesV2 - visibleCategoriesCount) })}
                <Image src={ArrowDownIcon} alt={'arrow down'} className="size-4 rotate-90" />
              </Button>
            )}
            {visibleCategoriesCount > limit && (
              <Button
                variant={'blank'}
                className="flex items-center gap-2 p-0 text-gray-500"
                onClick={handleShowLessCategories}
              >
                Show Less
                <Image src={ArrowDownIcon} alt={'arrow up'} className="size-4 -rotate-90" />
              </Button>
            )}
          </>
        )}
      </div>
    </>
  )
}

export default BodyParts
