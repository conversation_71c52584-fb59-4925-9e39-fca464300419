import { useTranslations } from 'next-intl'
import Image from 'next/image'
import React from 'react'

type SectionViewProps = {
  sectionData: any[]
  isViewImage?: boolean
  handleSelectCategory: (id: string, name: string) => void
}
const SectionView: React.FC<SectionViewProps> = ({
  sectionData,
  isViewImage = true,
  handleSelectCategory,
}) => {
  const t = useTranslations()

  return sectionData.length ? (
    <React.Fragment>
      {sectionData.map((section) => (
        <div
          onClick={() => handleSelectCategory(section.id, section.title)}
          key={section.id}
          className="flex cursor-pointer flex-col items-center justify-center gap-3 rounded-md border border-transparent bg-neutral-50 p-3 hover:border-primary-500 hover:text-primary-500"
        >
          {isViewImage && (
            <div className="h-9 w-9">
              {section.icon && (section.icon.thumbnailURL || section.icon.url) && (
                <Image
                  className="h-full w-full object-cover"
                  src={section.icon.thumbnailURL || section.icon.url}
                  alt={section.icon.filename}
                  width={36}
                  height={36}
                />
              )}
            </div>
          )}

          <div className="w-full">
            <span className="typo-body-6 line-clamp-2 text-center">{section.title}</span>
          </div>
        </div>
      ))}
    </React.Fragment>
  ) : (
    <div className="typo-body-3 col-span-3 flex w-full items-center justify-end text-danger-500">
      {t('MES-261')}.
    </div>
  )
}

export default SectionView
